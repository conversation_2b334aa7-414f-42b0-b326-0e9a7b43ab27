/* Premium Wire Shelves 3D Configurator Styles */

:root {
  /* Premium Color Palette */
  --premium-primary: #1e293b;
  --premium-secondary: #334155;
  --premium-accent: #3b82f6;
  --premium-accent-light: #60a5fa;
  --premium-accent-dark: #1d4ed8;
  --premium-success: #059669;
  --premium-warning: #d97706;
  --premium-surface: #ffffff;
  --premium-surface-elevated: #f8fafc;
  --premium-surface-muted: #f1f5f9;
  --premium-text-primary: #0f172a;
  --premium-text-secondary: #475569;
  --premium-text-muted: #64748b;
  --premium-border: #e2e8f0;
  --premium-border-muted: #f1f5f9;
  
  /* Premium Shadows */
  --shadow-soft: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-premium: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glow: 0 0 0 1px rgba(59, 130, 246, 0.05), 0 0 20px rgba(59, 130, 246, 0.1);
  
  /* Premium Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-accent: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-surface: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  
  /* Premium Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Premium Animations */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Premium Styles */
* {
  font-family: var(--font-primary);
}

body {
  font-feature-settings: 'kern' 1, 'liga' 1, 'frac' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Premium App Layout - Full Width */
.premium-app {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #f1f5f9 75%, #f8fafc 100%);
}

.premium-app-header {
  background: var(--gradient-card);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--premium-border);
  box-shadow: var(--shadow-soft);
  width: 100%;
}

.premium-app-header .max-w-7xl {
  max-width: none;
  width: 100%;
  padding: 0 3rem;
}

.premium-app > .max-w-7xl {
  max-width: none;
  width: 100%;
  padding: 0 3rem;
}

.premium-brand {
  flex-grow: 1;
}

.premium-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--premium-text-primary);
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.premium-subtitle {
  font-size: 1.125rem;
  color: var(--premium-text-secondary);
  font-weight: 500;
  margin-top: 0.5rem;
  letter-spacing: -0.01em;
}

.premium-status-indicators {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.premium-indicator-ai,
.premium-indicator-ready {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  border-radius: 1rem;
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: var(--transition-normal);
}

.premium-indicator-ai {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  color: white;
  box-shadow: var(--shadow-medium);
}

.premium-indicator-ready {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  box-shadow: var(--shadow-medium);
  animation: premium-pulse 2s infinite;
}

/* Premium Grid Layout - Full Screen Utilization */
.premium-grid {
  display: grid;
  grid-template-columns: 400px 1fr 380px;
  grid-template-rows: auto auto;
  grid-gap: 2.5rem;
  grid-template-areas: 
    "chat viewer controls"
    "bom bom bom";
  min-height: 80vh;
}

.premium-grid-chat { 
  grid-area: chat;
  min-width: 400px;
}

.premium-grid-viewer { 
  grid-area: viewer;
  min-width: 600px;
}

.premium-grid-controls { 
  grid-area: controls;
  min-width: 380px;
}

.premium-grid-bom { 
  grid-area: bom;
  margin-top: 1rem;
}

/* Premium Chat Interface - Larger and More Spacious */
.premium-card {
  background: var(--gradient-card);
  border-radius: 1.5rem;
  box-shadow: var(--shadow-large);
  border: 1px solid var(--premium-border);
  overflow: hidden;
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
  min-height: 600px;
}

.premium-card:hover {
  box-shadow: var(--shadow-premium);
  transform: translateY(-2px);
}

.premium-header {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-bottom: 1px solid var(--premium-border-muted);
  backdrop-filter: blur(5px);
  padding: 2rem;
}

/* Premium Chat Interface - Larger Avatar and Spacing */
.premium-avatar {
  width: 4rem;
  height: 4rem;
  background: var(--gradient-accent);
  border-radius: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-medium);
}

.premium-status-badge {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #059669;
}

.status-dot {
  width: 0.625rem;
  height: 0.625rem;
  background: #10b981;
  border-radius: 50%;
  margin-right: 0.75rem;
}

.premium-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.premium-scrollbar::-webkit-scrollbar-track {
  background: var(--premium-surface-muted);
  border-radius: 4px;
}

.premium-scrollbar::-webkit-scrollbar-thumb {
  background: var(--premium-text-muted);
  border-radius: 4px;
  opacity: 0.5;
}

.premium-scrollbar::-webkit-scrollbar-thumb:hover {
  opacity: 0.8;
}

.premium-user-message {
  background: var(--gradient-accent);
  box-shadow: var(--shadow-medium);
  font-size: 1rem;
  padding: 1.25rem 1.5rem;
}

.premium-ai-message {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid var(--premium-border);
  box-shadow: var(--shadow-soft);
  font-size: 1rem;
  padding: 1.25rem 1.5rem;
}

.premium-loading-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: var(--premium-text-muted);
  border-radius: 50%;
  animation: premium-bounce 1.4s infinite ease-in-out;
}

.premium-input-container {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-top: 1px solid var(--premium-border-muted);
  backdrop-filter: blur(5px);
  padding: 2rem;
}

.premium-input {
  width: 100%;
  padding: 1.25rem 1.75rem;
  border: 2px solid var(--premium-border);
  border-radius: 1.25rem;
  background: var(--premium-surface);
  color: var(--premium-text-primary);
  font-size: 1.125rem;
  font-weight: 500;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-soft);
}

.premium-input:focus {
  outline: none;
  border-color: var(--premium-accent);
  box-shadow: var(--shadow-glow);
}

.premium-send-button {
  padding: 1.25rem 2rem;
  background: var(--gradient-accent);
  color: white;
  border: none;
  border-radius: 1.25rem;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-medium);
  cursor: pointer;
}

.premium-send-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-large);
}

.premium-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Premium Parameter Controls - More Spacious */
.premium-ready-badge {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border: 1px solid #a7f3d0;
  border-radius: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--premium-success);
}

.premium-section-required {
  padding: 2rem;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 1.25rem;
  margin-bottom: 1.5rem;
}

.premium-section-optional {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid var(--premium-border);
  border-radius: 1.25rem;
}

.premium-section-title {
  font-size: 1.375rem;
  font-weight: 700;
  color: var(--premium-text-primary);
  letter-spacing: -0.025em;
  margin-bottom: 1.5rem;
}

.premium-input-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.premium-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--premium-text-primary);
  letter-spacing: 0.025em;
}

.premium-sublabel {
  font-size: 0.875rem;
  color: var(--premium-text-muted);
  margin-top: 0.375rem;
}

.premium-number-input,
.premium-select {
  padding: 1.125rem 1.5rem;
  border: 2px solid var(--premium-border);
  border-radius: 1rem;
  background: var(--premium-surface);
  color: var(--premium-text-primary);
  font-size: 1.125rem;
  font-weight: 500;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-soft);
}

.premium-number-input:focus,
.premium-select:focus {
  outline: none;
  border-color: var(--premium-accent);
  box-shadow: var(--shadow-glow);
}

.premium-toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: var(--premium-surface);
  border: 1px solid var(--premium-border);
  border-radius: 1rem;
  box-shadow: var(--shadow-soft);
  margin-bottom: 1.5rem;
}

.premium-toggle-info {
  flex-grow: 1;
}

.premium-toggle {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.75rem;
}

.premium-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--premium-border);
  transition: var(--transition-normal);
  border-radius: 1.75rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.premium-toggle-slider:before {
  position: absolute;
  content: "";
  height: 1.25rem;
  width: 1.25rem;
  left: 0.25rem;
  bottom: 0.25rem;
  background: white;
  transition: var(--transition-normal);
  border-radius: 50%;
  box-shadow: var(--shadow-soft);
}

.premium-toggle input:checked + .premium-toggle-slider {
  background: var(--premium-accent);
}

.premium-toggle input:checked + .premium-toggle-slider:before {
  transform: translateX(1.25rem);
}

/* Premium 3D Viewer */
.premium-3d-container {
  position: relative;
}

.premium-3d-badges {
  display: flex;
  gap: 0.75rem;
}

.premium-style-badge {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--premium-accent-dark);
}

.premium-finish-badge {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--premium-text-secondary);
}

.premium-3d-footer {
  margin-top: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
  border-radius: 0.75rem;
  text-align: center;
}

.premium-3d-footer p {
  font-size: 0.875rem;
  color: var(--premium-text-muted);
  font-weight: 500;
}

.premium-empty-3d {
  min-height: 600px;
  display: flex;
  align-items: center;
}

.premium-empty-state {
  width: 100%;
}

.premium-empty-icon {
  color: var(--premium-text-muted);
  opacity: 0.7;
}

.premium-requirements-card {
  display: inline-block;
  padding: 1.5rem;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 1rem;
  text-align: left;
  margin-top: 1rem;
}

/* Premium Bill of Materials */
.premium-spec-badge {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid var(--premium-border);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: var(--premium-text-secondary);
}

.premium-table-container {
  overflow-x: auto;
  border-radius: 1rem;
  border: 1px solid var(--premium-border);
  background: var(--premium-surface);
}

.premium-table {
  width: 100%;
  border-collapse: collapse;
}

.premium-table th {
  padding: 1.25rem 1rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid var(--premium-border);
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--premium-text-primary);
  text-align: left;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.premium-table td {
  padding: 1.25rem 1rem;
  border-bottom: 1px solid var(--premium-border-muted);
  font-size: 0.875rem;
  color: var(--premium-text-secondary);
  vertical-align: top;
}

.premium-table tr:hover {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.5) 0%, rgba(241, 245, 249, 0.5) 100%);
}

.premium-model-number {
  font-family: var(--font-mono);
  font-size: 0.75rem;
  padding: 0.5rem;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid var(--premium-border);
  border-radius: 0.5rem;
  color: var(--premium-text-primary);
  font-weight: 600;
}

.premium-category-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

/* Premium Animations */
@keyframes premium-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes premium-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes premium-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Premium Responsive Design - Better Breakpoints */
@media (max-width: 1800px) {
  .premium-grid {
    grid-template-columns: 380px 1fr 360px;
  }
}

@media (max-width: 1536px) {
  .premium-grid {
    grid-template-columns: 360px 1fr 340px;
  }
  
  .premium-app-header .max-w-7xl,
  .premium-app > .max-w-7xl {
    padding: 0 2rem;
  }
}

@media (max-width: 1280px) {
  .premium-grid {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "viewer"
      "chat"
      "controls"
      "bom";
    gap: 2rem;
  }
  
  .premium-grid-chat,
  .premium-grid-viewer,
  .premium-grid-controls {
    min-width: auto;
  }
  
  .premium-title {
    font-size: 2rem;
  }
  
  .premium-status-indicators {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .premium-app-header .max-w-7xl,
  .premium-app > .max-w-7xl {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .premium-app-header .max-w-7xl {
    padding: 1.5rem;
  }
  
  .premium-app > .max-w-7xl {
    padding: 1.5rem;
  }
  
  .premium-title {
    font-size: 1.75rem;
  }
  
  .premium-subtitle {
    font-size: 1rem;
  }
  
  .premium-grid {
    gap: 1.5rem;
  }
  
  .premium-card {
    border-radius: 1.25rem;
    min-height: auto;
  }
  
  .premium-header {
    padding: 1.5rem !important;
  }
  
  .premium-section-required,
  .premium-section-optional {
    padding: 1.5rem;
  }
  
  .premium-input-container {
    padding: 1.5rem;
  }
}

/* Premium Utilities */
.premium-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.premium-transition {
  transition: var(--transition-normal);
}

.premium-shadow-glow {
  box-shadow: var(--shadow-glow);
}

/* Print Styles */
@media print {
  .premium-app {
    background: white !important;
  }
  
  .premium-card {
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --premium-border: #000000;
    --premium-text-secondary: #000000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode variables can be added here in future versions */
}